@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\katex@0.16.21\node_modules\katex\node_modules;C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\katex@0.16.21\node_modules;C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\katex@0.16.21\node_modules\katex\node_modules;C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\katex@0.16.21\node_modules;C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\katex@0.16.21\node_modules\katex\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\katex@0.16.21\node_modules\katex\cli.js" %*
)
