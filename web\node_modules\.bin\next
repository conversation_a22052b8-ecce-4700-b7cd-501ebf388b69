#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/ORIGVMI/Github/Forks/dire-flow/web/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/bin/node_modules:/mnt/c/Users/<USER>/Desktop/ORIGVMI/Github/Forks/dire-flow/web/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/node_modules:/mnt/c/Users/<USER>/Desktop/ORIGVMI/Github/Forks/dire-flow/web/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/node_modules:/mnt/c/Users/<USER>/Desktop/ORIGVMI/Github/Forks/dire-flow/web/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules:/mnt/c/Users/<USER>/Desktop/ORIGVMI/Github/Forks/dire-flow/web/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/ORIGVMI/Github/Forks/dire-flow/web/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/bin/node_modules:/mnt/c/Users/<USER>/Desktop/ORIGVMI/Github/Forks/dire-flow/web/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/node_modules:/mnt/c/Users/<USER>/Desktop/ORIGVMI/Github/Forks/dire-flow/web/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/node_modules:/mnt/c/Users/<USER>/Desktop/ORIGVMI/Github/Forks/dire-flow/web/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules:/mnt/c/Users/<USER>/Desktop/ORIGVMI/Github/Forks/dire-flow/web/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../next/dist/bin/next" "$@"
else
  exec node  "$basedir/../next/dist/bin/next" "$@"
fi
