@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\dotenv-cli@8.0.0\node_modules\dotenv-cli\node_modules;C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\dotenv-cli@8.0.0\node_modules;C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\dotenv-cli@8.0.0\node_modules\dotenv-cli\node_modules;C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\dotenv-cli@8.0.0\node_modules;C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\dotenv-cli\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\dotenv-cli\cli.js" %*
)
