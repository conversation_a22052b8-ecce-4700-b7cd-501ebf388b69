@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\resolve@2.0.0-next.5\node_modules\resolve\bin\node_modules;C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\resolve@2.0.0-next.5\node_modules\resolve\node_modules;C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\resolve@2.0.0-next.5\node_modules;C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\resolve@2.0.0-next.5\node_modules\resolve\bin\node_modules;C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\resolve@2.0.0-next.5\node_modules\resolve\node_modules;C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\resolve@2.0.0-next.5\node_modules;C:\Users\<USER>\Desktop\ORIGVMI\Github\Forks\dire-flow\web\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\resolve@2.0.0-next.5\node_modules\resolve\bin\resolve" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\resolve@2.0.0-next.5\node_modules\resolve\bin\resolve" %*
)
